'use client';

import React, { useRef, useEffect, useMemo } from 'react';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';
import { RGBELoader } from 'three/examples/jsm/loaders/RGBELoader.js';
import { createPreciseKeycapGeometry, createKeycapMaterial } from './KeycapGeometry';

interface KeycapCustomization {
  keyId: string;
  textColor: string;
  fontSize: number;
  fontFamily: string;
  fontWeight: string;
  textStyle: string;
  textDecoration: string;
  backgroundImage?: string;
  backgroundOpacity: number;
  backgroundScale: number;
  backgroundPosition: { x: number; y: number };
  coverageMode: 'top-only' | 'full-keycap';
}

interface ThreeJSKeyboardProps {
  layoutId: string;
  selectedKeys: string[];
  onKeyClick: (keyId: string) => void;
  designElements: any[];
  keycapCustomizations?: Record<string, KeycapCustomization>;
  className?: string;
}

interface KeycapInfo {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  text: string;
  row: number;
}

const ThreeJSKeyboard: React.FC<ThreeJSKeyboardProps> = ({
  layoutId,
  selectedKeys,
  onKeyClick,
  designElements,
  keycapCustomizations = {},
  className = ''
}) => {
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene>();
  const rendererRef = useRef<THREE.WebGLRenderer>();
  const cameraRef = useRef<THREE.PerspectiveCamera>();
  const controlsRef = useRef<OrbitControls>();
  const keycapsRef = useRef<Map<string, THREE.Group>>(new Map());
  const raycasterRef = useRef<THREE.Raycaster>(new THREE.Raycaster());
  const mouseRef = useRef<THREE.Vector2>(new THREE.Vector2());

  // 键盘布局数据
  const keyboardLayout = useMemo<KeycapInfo[]>(() => {
    const layout: KeycapInfo[] = [];
    
    // 基于技术图2的精确尺寸计算
    // 标准键帽：17.2mm宽度 + 0.8mm间距 = 18.0mm总间距
    // 在像素坐标中，我们使用43像素作为标准键帽单位（包含间距）
    const KEY_UNIT = 43;  // 标准键帽单位（基于17.2mm + 0.8mm间距）
    const KEY_SIZE = 41;  // 实际键帽显示大小（基于17.2mm）
    
    if (layoutId === '108' || layoutId === '104') {
      // 第一行 - ESC和功能键（按照精确间距要求）
      layout.push({ id: 'esc', x: 10, y: 10, width: KEY_SIZE, height: 36, text: 'ESC', row: 1 });
      // F1-F4组 - ESC到F1距离19.8mm，F1-F4之间间距0.8mm
      layout.push({ id: 'f1', x: 67, y: 10, width: KEY_SIZE, height: 36, text: 'F1', row: 1 });
      layout.push({ id: 'f2', x: 112, y: 10, width: KEY_SIZE, height: 36, text: 'F2', row: 1 });
      layout.push({ id: 'f3', x: 157, y: 10, width: KEY_SIZE, height: 36, text: 'F3', row: 1 });
      layout.push({ id: 'f4', x: 202, y: 10, width: KEY_SIZE, height: 36, text: 'F4', row: 1 });
      // F5-F8组 - F4到F5距离10.3mm
      layout.push({ id: 'f5', x: 275, y: 10, width: KEY_SIZE, height: 36, text: 'F5', row: 1 });
      layout.push({ id: 'f6', x: 320, y: 10, width: KEY_SIZE, height: 36, text: 'F6', row: 1 });
      layout.push({ id: 'f7', x: 365, y: 10, width: KEY_SIZE, height: 36, text: 'F7', row: 1 });
      layout.push({ id: 'f8', x: 410, y: 10, width: KEY_SIZE, height: 36, text: 'F8', row: 1 });
      // F9-F12组 - F8到F9距离10.3mm
      layout.push({ id: 'f9', x: 483, y: 10, width: KEY_SIZE, height: 36, text: 'F9', row: 1 });
      layout.push({ id: 'f10', x: 528, y: 10, width: KEY_SIZE, height: 36, text: 'F10', row: 1 });
      layout.push({ id: 'f11', x: 573, y: 10, width: KEY_SIZE, height: 36, text: 'F11', row: 1 });
      layout.push({ id: 'f12', x: 618, y: 10, width: KEY_SIZE, height: 36, text: 'F12', row: 1 });
      // 右上功能键组 - F12到PRT距离5.8mm
      layout.push(
        { id: 'prt', x: 678, y: 10, width: KEY_SIZE, height: 36, text: 'PRT', row: 1 },
        { id: 'scr', x: 723, y: 10, width: KEY_SIZE, height: 36, text: 'SCR', row: 1 },
        { id: 'pau', x: 768, y: 10, width: KEY_SIZE, height: 36, text: 'PAU', row: 1 }
      );

      // 第二行 - 数字键
      layout.push({ id: 'grave', x: 10, y: 60, width: KEY_SIZE, height: 36, text: '~', row: 2 });
      ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0', '-', '='].forEach((key, i) => {
        const keyId = key === '-' ? 'minus' : key === '=' ? 'equal' : `key-${key}`;
        layout.push({ id: keyId, x: 65 + i * KEY_UNIT, y: 60, width: KEY_SIZE, height: 36, text: key, row: 2 });
      });
      layout.push({ id: 'backspace', x: 689, y: 60, width: 72, height: 36, text: 'BACK', row: 2 });
      layout.push(
        { id: 'ins', x: 785, y: 60, width: KEY_SIZE, height: 36, text: 'INS', row: 2 },
        { id: 'home', x: 840, y: 60, width: KEY_SIZE, height: 36, text: 'HM', row: 2 },
        { id: 'pageup', x: 895, y: 60, width: KEY_SIZE, height: 36, text: 'UP', row: 2 }
      );

      // 第三行 - QWERTY
      layout.push({ id: 'tab', x: 10, y: 105, width: 72, height: 36, text: 'TAB', row: 3 });
      ['Q', 'W', 'E', 'R', 'T', 'Y', 'U', 'I', 'O', 'P', '[', ']', '\\'].forEach((key, i) => {
        const keyId = key === '[' ? 'lbracket' : key === ']' ? 'rbracket' : key === '\\' ? 'backslash' : key.toLowerCase();
        layout.push({ id: keyId, x: 87 + i * KEY_SIZE, y: 105, width: KEY_SIZE, height: 36, text: key, row: 3 });
      });
      layout.push(
        { id: 'del', x: 785, y: 105, width: KEY_SIZE, height: 36, text: 'DEL', row: 3 },
        { id: 'end', x: 840, y: 105, width: KEY_SIZE, height: 36, text: 'END', row: 3 },
        { id: 'pagedown', x: 895, y: 105, width: KEY_SIZE, height: 36, text: 'DN', row: 3 }
      );

      // 第四行 - ASDF
      layout.push({ id: 'caps', x: 10, y: 150, width: 84, height: 36, text: 'CAPS', row: 4 });
      ['A', 'S', 'D', 'F', 'G', 'H', 'J', 'K', 'L', ';', "'"].forEach((key, i) => {
        const keyId = key === ';' ? 'semicolon' : key === "'" ? 'quote' : key.toLowerCase();
        layout.push({ id: keyId, x: 99 + i * KEY_SIZE, y: 150, width: KEY_SIZE, height: 36, text: key, row: 4 });
      });
      layout.push({ id: 'enter', x: 627, y: 150, width: 84, height: 36, text: 'ENTER', row: 4 });

      // 第五行 - ZXCV
      layout.push({ id: 'lshift', x: 10, y: 195, width: 108, height: 36, text: 'SHIFT', row: 5 });
      ['Z', 'X', 'C', 'V', 'B', 'N', 'M', ',', '.', '/'].forEach((key, i) => {
        const keyId = key === ',' ? 'comma' : key === '.' ? 'period' : key === '/' ? 'slash' : key.toLowerCase();
        layout.push({ id: keyId, x: 123 + i * KEY_SIZE, y: 195, width: KEY_SIZE, height: 36, text: key, row: 5 });
      });
      layout.push(
        { id: 'rshift', x: 603, y: 195, width: 108, height: 36, text: 'SHIFT', row: 5 },
        { id: 'up', x: 840, y: 195, width: KEY_SIZE, height: 36, text: '↑', row: 5 }
      );

      // 第六行 - 底部控制键
      layout.push(
        { id: 'lctrl', x: 10, y: 240, width: 60, height: 36, text: 'CTRL', row: 6 },
        { id: 'lwin', x: 75, y: 240, width: 60, height: 36, text: 'WIN', row: 6 },
        { id: 'lalt', x: 140, y: 240, width: 60, height: 36, text: 'ALT', row: 6 },
        { id: 'space', x: 205, y: 240, width: 264, height: 36, text: 'SPACE', row: 6 },
        { id: 'ralt', x: 474, y: 240, width: 60, height: 36, text: 'ALT', row: 6 },
        { id: 'fn', x: 539, y: 240, width: 60, height: 36, text: 'FN', row: 6 },
        { id: 'menu', x: 604, y: 240, width: 60, height: 36, text: 'MENU', row: 6 },
        { id: 'rctrl', x: 669, y: 240, width: 60, height: 36, text: 'CTRL', row: 6 },
        { id: 'left', x: 785, y: 240, width: KEY_SIZE, height: 36, text: '←', row: 6 },
        { id: 'down', x: 840, y: 240, width: KEY_SIZE, height: 36, text: '↓', row: 6 },
        { id: 'right', x: 895, y: 240, width: KEY_SIZE, height: 36, text: '→', row: 6 }
      );

      // 小键盘区域
      if (layoutId === '108') {
        layout.push(
          { id: 'numlock', x: 970, y: 60, width: KEY_SIZE, height: 36, text: 'NUM', row: 2 },
          { id: 'numdiv', x: 1025, y: 60, width: KEY_SIZE, height: 36, text: '/', row: 2 },
          { id: 'nummul', x: 1080, y: 60, width: KEY_SIZE, height: 36, text: '*', row: 2 },
          { id: 'numsub', x: 1135, y: 60, width: KEY_SIZE, height: 36, text: '-', row: 2 },
          { id: 'num7', x: 970, y: 105, width: KEY_SIZE, height: 36, text: '7', row: 3 },
          { id: 'num8', x: 1025, y: 105, width: KEY_SIZE, height: 36, text: '8', row: 3 },
          { id: 'num9', x: 1080, y: 105, width: KEY_SIZE, height: 36, text: '9', row: 3 },
          { id: 'numadd', x: 1135, y: 105, width: KEY_SIZE, height: 81, text: '+', row: 3 },
          { id: 'num4', x: 970, y: 150, width: KEY_SIZE, height: 36, text: '4', row: 4 },
          { id: 'num5', x: 1025, y: 150, width: KEY_SIZE, height: 36, text: '5', row: 4 },
          { id: 'num6', x: 1080, y: 150, width: KEY_SIZE, height: 36, text: '6', row: 4 },
          { id: 'num1', x: 970, y: 195, width: KEY_SIZE, height: 36, text: '1', row: 5 },
          { id: 'num2', x: 1025, y: 195, width: KEY_SIZE, height: 36, text: '2', row: 5 },
          { id: 'num3', x: 1080, y: 195, width: KEY_SIZE, height: 36, text: '3', row: 5 },
          { id: 'numenter', x: 1135, y: 195, width: KEY_SIZE, height: 81, text: 'ENT', row: 5 },
          { id: 'num0', x: 970, y: 240, width: 103, height: 36, text: '0', row: 6 },
          { id: 'numdot', x: 1080, y: 240, width: KEY_SIZE, height: 36, text: '.', row: 6 }
        );
      }
    }

    return layout;
  }, [layoutId]);

  // 【已迁移到KeycapGeometry.tsx】- 使用新的精确键帽几何体

  // 【已迁移到KeycapGeometry.tsx】- 使用导入的专业PBT键帽材质

  // 创建键帽侧壁材质（哑光效果）
  const createKeycapSideMaterial = () => {
    return new THREE.MeshPhysicalMaterial({
      color: 0xe5e5e5,
      roughness: 0.9,        // 哑光侧壁
      metalness: 0.0,
      clearcoat: 0.0,
      reflectivity: 0.1
    });
  };

  // 创建文字纹理
  const createTextTexture = (text: string, fontSize: number = 24) => {
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d')!;
    
    canvas.width = 512;
    canvas.height = 256;
    
    // 设置字体样式
    context.fillStyle = '#ffffff';
    context.fillRect(0, 0, canvas.width, canvas.height);
    
    context.fillStyle = '#000000';
    context.font = `bold ${fontSize}px 'Orbitron', 'Arial Black', Arial, sans-serif`;
    context.textAlign = 'center';
    context.textBaseline = 'middle';
    
    // 绘制文字
    context.fillText(text, canvas.width / 2, canvas.height / 2);
    
    const texture = new THREE.CanvasTexture(canvas);
    texture.minFilter = THREE.LinearFilter;
    texture.wrapS = THREE.ClampToEdgeWrapping;
    texture.wrapT = THREE.ClampToEdgeWrapping;
    
    return texture;
  };

  // 创建键帽组
  const createKeycap = (keyInfo: KeycapInfo) => {
    const group = new THREE.Group();
    group.userData = { keyId: keyInfo.id, clickable: true };
    
    // 坐标转换 - 优化键帽位置
    const scale = 0.28; // 适中的缩放因子
    // 将键盘居中：考虑实际键盘尺寸
    const centerX = 1220 / 2;
    const centerZ = 400 / 2;
    
    // 计算键帽位置，考虑高度偏移
    const yOffset = keyInfo.row * 0.5; // 不同行有不同高度
    group.position.set(
      (keyInfo.x - centerX) * scale,      // X坐标
      -2 + yOffset,                       // Y坐标（考虑行高）
      (keyInfo.y - centerZ) * scale * 0.9 // Z坐标（稍微压缩深度）
    );
    
    // 添加行倾斜角度
    const rowAngle = (keyInfo.row - 3) * Math.PI / 24; // 每行倾斜角度
    group.rotation.x = rowAngle;

    // 使用精确尺寸的键帽几何体
    const geometry = createPreciseKeycapGeometry();
    
    // 检查是否有自定义设计 - 考虑键盘整体偏移
    const hasCustomDesign = designElements.some(el => {
      // 键帽在SVG中的实际位置需要考虑30px的键盘偏移
      const keycapCenterX = keyInfo.x + keyInfo.width / 2;
      const keycapCenterY = keyInfo.y + keyInfo.height / 2 + 30; // 加上键盘偏移
      return el.x <= keycapCenterX &&
             keycapCenterX <= el.x + el.width &&
             el.y <= keycapCenterY &&
             keycapCenterY <= el.y + el.height;
    });

    // 获取键帽自定义设置
    const customization = keycapCustomizations[keyInfo.id];
    const isSelected = selectedKeys.includes(keyInfo.id);

    // 创建键帽材质
    let material = createKeycapMaterial(isSelected ? 0x4285f4 : 0xf8f8f8);

    // 如果有背景图像且覆盖模式为全键帽，则应用纹理
    if (customization?.backgroundImage && customization.coverageMode === 'full-keycap') {
      const textureLoader = new THREE.TextureLoader();
      const texture = textureLoader.load(customization.backgroundImage);
      texture.wrapS = THREE.RepeatWrapping;
      texture.wrapT = THREE.RepeatWrapping;

      material = createKeycapMaterial(isSelected ? 0x4285f4 : 0xffffff);
      material.map = texture;
      material.transparent = true;
      material.opacity = customization.backgroundOpacity;
    }

    const keycap = new THREE.Mesh(geometry, material);
    keycap.castShadow = true;
    keycap.receiveShadow = true;

    group.add(keycap);

    // 添加文字平面
    const textTexture = createTextTexture(keyInfo.text);
    const textMaterial = new THREE.MeshBasicMaterial({ 
      map: textTexture, 
      transparent: true,
      alphaTest: 0.1
    });
    
    const textGeometry = new THREE.PlaneGeometry(keyInfo.width * scale * 0.8, keyInfo.height * scale * 0.8);
    const textMesh = new THREE.Mesh(textGeometry, textMaterial);
    textMesh.position.z = 1.2; // 文字浮在键帽表面
    textMesh.rotation.x = -Math.PI / 2;
    
    group.add(textMesh);

    return group;
  };

  // 初始化Three.js场景
  useEffect(() => {
    if (!mountRef.current) return;

    console.log('3D键盘初始化开始...');

    // 创建场景
    const scene = new THREE.Scene();
    scene.background = new THREE.Color(0xf5f5f5);
    sceneRef.current = scene;

    // 创建相机 - 优化视角以匹配图1效果
    const width = mountRef.current.clientWidth || 800;
    const height = mountRef.current.clientHeight || 400;
    const aspectRatio = width / height;
    const camera = new THREE.PerspectiveCamera(35, aspectRatio, 0.1, 2000);
    camera.position.set(-50, 200, 300);  // 调整到更好的观察位置
    camera.lookAt(0, -10, 0);            // 稍微向下看以更好地观察键盘
    cameraRef.current = camera;

    console.log('相机设置完成，位置:', camera.position, '尺寸:', width, 'x', height);

    // 创建渲染器
    const renderer = new THREE.WebGLRenderer({ 
      antialias: true,
      alpha: false,
      powerPreference: "high-performance"
    });
    
    // 动态设置渲染器大小
    const resizeRenderer = () => {
      const width = mountRef.current?.clientWidth || 800;
      const height = mountRef.current?.clientHeight || 400;
      renderer.setSize(width, height);
      camera.aspect = width / height;
      camera.updateProjectionMatrix();
    };
    
    resizeRenderer();
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
    renderer.shadowMap.enabled = true;
    renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    renderer.toneMapping = THREE.ACESFilmicToneMapping;
    renderer.toneMappingExposure = 1.0;
    renderer.setClearColor(0xf5f5f5, 1);
    rendererRef.current = renderer;

    mountRef.current.appendChild(renderer.domElement);

    // 添加控制器 - 优化交互体验
    const controls = new OrbitControls(camera, renderer.domElement);
    controls.enableDamping = true;
    controls.dampingFactor = 0.08;      // 更平滑的阻尼
    controls.minDistance = 60;          // 最小缩放距离
    controls.maxDistance = 250;         // 最大缩放距离
    controls.maxPolarAngle = Math.PI / 2.1;  // 限制垂直旋转
    controls.minPolarAngle = Math.PI / 8;    // 防止过度俯视
    controls.enablePan = true;          // 允许平移
    controls.panSpeed = 0.8;            // 平移速度
    controls.rotateSpeed = 0.5;         // 旋转速度
    controls.zoomSpeed = 0.6;           // 缩放速度
    controls.autoRotate = false;        // 关闭自动旋转
    controlsRef.current = controls;

    // 专业光照系统 - 完全符合图1效果
    const lights = {
      // 环境光 - 柔和的基础照明
      ambient: new THREE.AmbientLight(0xffffff, 0.4),
      
      // 半球光 - 模拟天空和地面反射
      hemisphere: new THREE.HemisphereLight(0xffffff, 0xffffff, 0.6),
      
      // 主光源 - 强烈的定向光（45度角）
      main: new THREE.DirectionalLight(0xffffff, 1.4)
    };
    
    // 设置光源位置
    lights.hemisphere.position.set(0, 100, 0);
    lights.main.position.set(100, 100, 100);
    
    // 配置主光源阴影
    lights.main.castShadow = true;
    lights.main.shadow.mapSize.width = 4096;  // 高质量阴影
    lights.main.shadow.mapSize.height = 4096;
    lights.main.shadow.camera.near = 1;
    lights.main.shadow.camera.far = 500;
    lights.main.shadow.camera.left = -200;
    lights.main.shadow.camera.right = 200;
    lights.main.shadow.camera.top = 200;
    lights.main.shadow.camera.bottom = -200;
    lights.main.shadow.bias = -0.0005;      // 增强阴影深度
    lights.main.shadow.normalBias = 0.02;   // 减少阴影伪影
    
    // 添加所有光源到场景
    Object.values(lights).forEach(light => scene.add(light));

    // 扩展光照系统
    const secondaryLights = {
      // 轮廓光 - 增强边缘高光
      rim: new THREE.DirectionalLight(0xffffff, 0.6),
      
      // 填充光 - 柔和阴影
      fill: new THREE.DirectionalLight(0xffffff, 0.3),
      
      // 背面轮廓光 - 增强立体感
      back: new THREE.DirectionalLight(0xffffff, 0.4),
      
      // 顶部补充光 - 增强整体照明
      topHemi: new THREE.HemisphereLight(0xffffff, 0xffffff, 0.6),
      
      // 顶部定向光 - 增强键帽顶部照明
      topDir: new THREE.DirectionalLight(0xffffff, 0.4),
      
      // 点光源 - 增加局部高光
      spot: new THREE.SpotLight(0xffffff, 0.3, 200, Math.PI / 6, 0.5)
    };
    
    // 设置光源位置
    secondaryLights.rim.position.set(-150, 100, 150);
    secondaryLights.fill.position.set(80, -50, -100);
    secondaryLights.back.position.set(-50, 80, -180);
    secondaryLights.topHemi.position.set(0, 100, 0);
    secondaryLights.topDir.position.set(0, 150, 0);
    secondaryLights.spot.position.set(60, 100, 60);
    secondaryLights.spot.target.position.set(0, 0, 0);
    
    // 添加所有次要光源到场景
    Object.entries(secondaryLights).forEach(([key, light]) => {
      scene.add(light);
      if (light instanceof THREE.SpotLight) {
        scene.add(light.target);
      }
    });

    // 添加高质量键盘底板 - 完全匹配图1效果
    const baseGeometry = new THREE.BoxGeometry(350, 6, 150);  // 更大的底板尺寸
    const baseMaterial = new THREE.MeshPhysicalMaterial({
      color: 0x1a1a1a,          // 更深的底板颜色
      metalness: 0.85,          // 金属感
      roughness: 0.3,           // 适中的光滑度
      clearcoat: 0.7,           // 适中的涂层
      clearcoatRoughness: 0.2,
      reflectivity: 0.7,
      ior: 1.5,
      // 增强立体感
      sheen: 0.2,
      sheenRoughness: 0.5,
      sheenColor: new THREE.Color(0x303030)
    });
    const base = new THREE.Mesh(baseGeometry, baseMaterial);
    base.position.set(0, -8, 0);  // 调整底板位置
    base.receiveShadow = true;
    base.castShadow = true;
    scene.add(base);

    // 添加底板边框
    const edgeGeometry = new THREE.BoxGeometry(360, 2, 160);
    const edgeMaterial = new THREE.MeshPhysicalMaterial({
      color: 0x252525,
      metalness: 0.9,
      roughness: 0.2,
      clearcoat: 0.8
    });
    const edge = new THREE.Mesh(edgeGeometry, edgeMaterial);
    edge.position.set(0, -10, 0);
    edge.receiveShadow = true;
    scene.add(edge);

    // 添加键盘边框
    const frameGeometry = new THREE.BoxGeometry(205, 1, 95);
    const frameMaterial = new THREE.MeshPhysicalMaterial({
      color: 0x333333,
      metalness: 0.9,
      roughness: 0.1,
      clearcoat: 0.8
    });
    const frame = new THREE.Mesh(frameGeometry, frameMaterial);
    frame.position.y = -8;
    frame.receiveShadow = true;
    scene.add(frame);

    // 创建键帽
    console.log('开始创建键帽，数量:', keyboardLayout.length);
    console.log('前5个键帽布局数据:', keyboardLayout.slice(0, 5));
    
    keyboardLayout.forEach((keyInfo, index) => {
      const keycap = createKeycap(keyInfo);
      scene.add(keycap);
      keycapsRef.current.set(keyInfo.id, keycap);
      
      // 调试：为前几个键帽添加边界框
      if (index < 20) {
        const boxHelper = new THREE.BoxHelper(keycap, 0xff0000);
        scene.add(boxHelper);
        console.log(`键帽 ${index}:`, keyInfo.id, 'SVG坐标:', {x: keyInfo.x, y: keyInfo.y}, '3D位置:', keycap.position);
      }
    });
    console.log('键帽创建完成，场景中的对象数量:', scene.children.length);

    // 鼠标点击事件
    const handleClick = (event: MouseEvent) => {
      const rect = renderer.domElement.getBoundingClientRect();
      mouseRef.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      mouseRef.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

      raycasterRef.current.setFromCamera(mouseRef.current, camera);
      const intersects = raycasterRef.current.intersectObjects(scene.children, true);

      if (intersects.length > 0) {
        const object = intersects[0].object;
        let group = object.parent;
        
        // 找到键帽组
        while (group && !group.userData.keyId) {
          group = group.parent;
        }
        
        if (group && group.userData.keyId && group.userData.clickable) {
          onKeyClick(group.userData.keyId);
        }
      }
    };

    renderer.domElement.addEventListener('click', handleClick);
    console.log('事件监听器添加完成');

    // 渲染循环
    const animate = () => {
      requestAnimationFrame(animate);
      controls.update();
      renderer.render(scene, camera);
    };
    
    console.log('开始渲染循环');
    animate();

    // 清理函数
    return () => {
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement);
      }
      renderer.domElement.removeEventListener('click', handleClick);
      renderer.dispose();
    };
  }, [keyboardLayout, onKeyClick]);

  // 更新选中状态
  useEffect(() => {
    keyboardLayout.forEach(keyInfo => {
      const keycap = keycapsRef.current.get(keyInfo.id);
      if (keycap) {
        const mesh = keycap.children.find(child => 
          child instanceof THREE.Mesh && 
          child.material instanceof THREE.MeshPhysicalMaterial
        ) as THREE.Mesh<THREE.BufferGeometry, THREE.MeshPhysicalMaterial> | undefined;
        
        if (mesh && mesh.material) {
          const isSelected = selectedKeys.includes(keyInfo.id);
          const hasCustomDesign = designElements.some(el => {
            // 键帽在SVG中的实际位置需要考虑30px的键盘偏移
            const keycapCenterX = keyInfo.x + keyInfo.width / 2;
            const keycapCenterY = keyInfo.y + keyInfo.height / 2 + 30; // 加上键盘偏移
            return el.x <= keycapCenterX &&
                   keycapCenterX <= el.x + el.width &&
                   el.y <= keycapCenterY &&
                   keycapCenterY <= el.y + el.height;
          });
          
          // 获取键帽自定义设置
          const customization = keycapCustomizations[keyInfo.id];

          // 更新材质
          if (isSelected) {
            mesh.material.color.setHex(0x3b82f6);
            mesh.material.emissive.setHex(0x1e3a8a);
            mesh.material.emissiveIntensity = 0.1;
          } else {
            // 如果有背景图像且为全键帽覆盖模式，保持白色基础色
            const hasBackgroundImage = customization?.backgroundImage && customization.coverageMode === 'full-keycap';
            mesh.material.color.setHex(hasBackgroundImage || hasCustomDesign ? 0xffffff : 0xf8f8f8);
            mesh.material.emissive.setHex(0x000000);
            mesh.material.emissiveIntensity = 0;
          }

          // 更新背景图像纹理和透明度
          if (customization?.backgroundImage && customization.coverageMode === 'full-keycap') {
            if (!mesh.material.map || mesh.material.map.image?.src !== customization.backgroundImage) {
              const textureLoader = new THREE.TextureLoader();
              const texture = textureLoader.load(customization.backgroundImage);
              texture.wrapS = THREE.RepeatWrapping;
              texture.wrapT = THREE.RepeatWrapping;
              mesh.material.map = texture;
              mesh.material.needsUpdate = true;
            }
            mesh.material.transparent = true;
            mesh.material.opacity = customization.backgroundOpacity;
          } else {
            // 清除纹理
            if (mesh.material.map) {
              mesh.material.map = null;
              mesh.material.transparent = false;
              mesh.material.opacity = 1;
              mesh.material.needsUpdate = true;
            }
          }
        }
      }
    });
  }, [selectedKeys, designElements, keyboardLayout, keycapCustomizations]);

  return (
    <div className={`w-full h-full ${className}`}>
      <div 
        ref={mountRef} 
        className="w-full h-full relative"
        style={{ minHeight: '400px' }}
      >
        {/* 添加加载提示 */}
        <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded z-10">
          3D渲染中... 键盘数量: {keyboardLayout.length}
        </div>
      </div>
    </div>
  );
};

export default ThreeJSKeyboard; 